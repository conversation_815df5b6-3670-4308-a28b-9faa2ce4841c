'use client';

import { useState, useEffect, useMemo, memo } from 'react';
import { Button } from '@/features/shared/components/ui/button';
import { Label } from '@/features/shared/components/ui/label';
import { Badge } from '@/features/shared/components/ui/badge';
import { Switch } from '@/features/shared/components/ui/switch';
import { UserAutocomplete } from '@/features/shared/components/UserAutocomplete';
import { ProfileAvatar } from '@/features/shared/components/ProfileAvatar';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/features/shared/components/ui/tooltip';
import { useAdminSettings } from '../hooks/useSettingsSync';
import { useUserDetails } from '@/features/shared/hooks/useUserSearch';
import { DEPARTMENT_CONFIG, type Department } from '../models/settings.schema';
import { cn } from '@/lib/utils';
import { Save, RotateCcw, AlertCircle, Users, ArrowRight } from 'lucide-react';
import { toast } from '@/features/shared/components/toast';

interface DepartmentRule {
  department: Department;
  assigned_agent_id: string | null;
  is_active: boolean;
}

const DepartmentAssignmentRulesComponent = () => {
  const { adminSettings, updateDepartmentRules, isLoading } =
    useAdminSettings();
  const [rules, setRules] = useState<DepartmentRule[]>([]);
  const [hasChanges, setHasChanges] = useState(false);

  // Get agent details for toast messages
  const assignedAgentIds = useMemo(() => {
    return rules
      .filter((rule) => rule.assigned_agent_id)
      .map((rule) => rule.assigned_agent_id!);
  }, [rules]);

  const { users: agentDetails } = useUserDetails(assignedAgentIds);

  // Track if we're in the middle of a save operation to prevent re-initialization
  const [isSaving, setIsSaving] = useState(false);
  // Track recent successful saves to prevent immediate override by real-time sync
  const [lastSaveTime, setLastSaveTime] = useState<number>(0);
  // Track the last saved state to prevent overriding with stale data
  const [lastSavedRules, setLastSavedRules] = useState<DepartmentRule[]>([]);

  // Initialize rules from admin settings
  useEffect(() => {
    // Don't re-initialize if we're currently saving to prevent clearing updates
    if (isSaving) return;

    // Don't re-initialize immediately after a successful save to prevent real-time sync conflicts
    const timeSinceLastSave = Date.now() - lastSaveTime;
    if (timeSinceLastSave < 2000) {
      // If we have recently saved rules, use those instead of potentially stale API data
      if (lastSavedRules.length > 0) {
        setRules(lastSavedRules);
        setHasChanges(false);
      }
      return;
    }

    const currentRules = adminSettings?.auto_assignment_rules || [];
    const initialRules: DepartmentRule[] = Object.keys(DEPARTMENT_CONFIG).map(
      (dept) => {
        const department = dept as Department;
        const existingRule = currentRules.find(
          (rule) => rule.department === department
        );

        return {
          department,
          assigned_agent_id: existingRule?.assigned_agent_id || null,
          // Use the actual is_active value from the database, default to true if no rule exists
          is_active: existingRule?.is_active ?? true,
        };
      }
    );

    setRules(initialRules);
    setHasChanges(false);
  }, [adminSettings, isSaving, lastSaveTime, lastSavedRules]);

  const handleAgentChange = (
    department: Department,
    agentId: string | string[]
  ) => {
    const newAgentId = Array.isArray(agentId)
      ? agentId[0] || null
      : agentId || null;

    setRules((prev) =>
      prev.map((rule) =>
        rule.department === department
          ? {
              ...rule,
              assigned_agent_id: newAgentId,
              // Automatically enable when agent is assigned, disable when removed
              is_active: !!newAgentId,
            }
          : rule
      )
    );
    setHasChanges(true);
  };

  const handleToggleActive = async (department: Department) => {
    const currentRule = rules.find((rule) => rule.department === department);
    if (!currentRule) return;

    const newActiveState = !currentRule.is_active;
    const departmentLabel = DEPARTMENT_CONFIG[department].label;

    try {
      setIsSaving(true);

      // Create the updated rule for API
      const updatedRule = {
        department: currentRule.department,
        assigned_agent_id: currentRule.assigned_agent_id,
        is_active: newActiveState,
        priority: 1,
      };

      // Send only this specific rule to the API and wait for response
      await updateDepartmentRules([updatedRule]);

      // Update local state directly after successful API call
      setRules((prev) =>
        prev.map((rule) =>
          rule.department === department
            ? { ...rule, is_active: newActiveState }
            : rule
        )
      );

      // Get agent details for notification
      const assignedAgent = currentRule.assigned_agent_id
        ? agentDetails.find(
            (agent) => agent.id === currentRule.assigned_agent_id
          )
        : null;

      // Show appropriate notification based on the new state
      if (newActiveState && currentRule.assigned_agent_id) {
        // Activation notification
        const agentName = assignedAgent?.name || 'Unknown Agent';
        const agentEmail = assignedAgent?.email || '';
        toast.success(`${departmentLabel} Department Activated`, {
          description: `${agentName}${agentEmail ? ` (${agentEmail})` : ''} activated for auto-assignment`,
        });
      } else {
        // Deactivation notification
        toast.success(`${departmentLabel} Department Deactivated`, {
          description:
            'Auto-assignment deactivated - tickets will use default agent',
        });
      }

      // Mark the save time and store the saved rules to prevent override
      setLastSaveTime(Date.now());
      setLastSavedRules(updatedRules);
      setTimeout(() => {
        setIsSaving(false);
      }, 500);
    } catch (error) {
      // Enhanced error handling with server validation messages
      let errorMessage =
        'Please try again or contact support if the problem persists.';

      if (error instanceof Error) {
        // Check if it's a server validation error
        try {
          const errorData = JSON.parse(error.message);
          if (errorData.details) {
            errorMessage = errorData.details;
          }
        } catch {
          // If not JSON, use the error message directly
          errorMessage = error.message;
        }
      }

      toast.error('Failed to Update Department Rule', {
        description: errorMessage,
      });

      setIsSaving(false);
    }
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);

      // Convert ALL rules to API format (including those without agents)
      // This ensures the API can properly handle targeted updates vs deactivation
      const apiRules = rules.map((rule, index) => ({
        department: rule.department,
        assigned_agent_id: rule.assigned_agent_id,
        is_active: rule.is_active,
        priority: index + 1, // Set priority based on order
      }));

      await updateDepartmentRules(apiRules);
      setHasChanges(false);

      // Enhanced toast message with details - only count rules with assigned agents
      const activeRulesCount = apiRules.filter(
        (rule) => rule.is_active && rule.assigned_agent_id
      ).length;
      const departmentNames = apiRules
        .filter((rule) => rule.is_active && rule.assigned_agent_id)
        .map((rule) => DEPARTMENT_CONFIG[rule.department].label)
        .join(', ');

      if (activeRulesCount > 0) {
        toast.success('Department Assignment Rules Updated', {
          description: `${activeRulesCount} department${activeRulesCount === 1 ? '' : 's'} configured: ${departmentNames}`,
        });
      } else {
        toast.success('Department Assignment Rules Cleared', {
          description:
            'All department-specific assignment rules have been removed',
        });
      }
    } catch (error) {
      // Enhanced error handling with server validation messages
      let errorMessage =
        'Unable to save department assignment rules. Please try again.';

      if (error instanceof Error) {
        // Check if it's a server validation error
        try {
          const errorData = JSON.parse(error.message);
          if (errorData.details) {
            errorMessage = errorData.details;
          }
        } catch {
          // If not JSON, use the error message directly
          errorMessage = error.message;
        }
      }

      toast.error('Update Failed', {
        description: errorMessage,
      });
      console.error('Department rules update error:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = () => {
    const currentRules = adminSettings?.auto_assignment_rules || [];
    const resetRules: DepartmentRule[] = Object.keys(DEPARTMENT_CONFIG).map(
      (dept) => {
        const department = dept as Department;
        const existingRule = currentRules.find(
          (rule) => rule.department === department
        );

        return {
          department,
          assigned_agent_id: existingRule?.assigned_agent_id || null,
          // Department rules are active by default, can be toggled independently
          is_active: existingRule?.is_active ?? true,
        };
      }
    );

    setRules(resetRules);
    setHasChanges(false);
  };

  const activeRulesCount = rules.filter(
    (rule) => rule.is_active && rule.assigned_agent_id
  ).length;

  // Group agents by their assignments for avatar display
  const agentAssignments = useMemo(() => {
    const assignments = new Map<
      string,
      { agent: any; departments: Department[] }
    >();

    rules
      .filter((rule) => rule.is_active && rule.assigned_agent_id)
      .forEach((rule) => {
        const agentId = rule.assigned_agent_id!;
        const agent = agentDetails.find((a) => a.id === agentId);

        if (agent) {
          if (assignments.has(agentId)) {
            assignments.get(agentId)!.departments.push(rule.department);
          } else {
            assignments.set(agentId, { agent, departments: [rule.department] });
          }
        }
      });

    return Array.from(assignments.values());
  }, [rules, agentDetails]);

  return (
    <div className='space-y-6'>
      {/* Status Overview */}
      <div className='flex items-center gap-3 p-4 bg-muted/50 rounded-lg'>
        {/* Avatar Display - Show first assigned agent's avatar */}
        {agentAssignments.length > 0 && agentAssignments[0]?.agent ? (
          <Tooltip>
            <TooltipTrigger asChild>
              <div>
                <ProfileAvatar
                  avatarUrl={agentAssignments[0].agent.avatar_url || null}
                  name={agentAssignments[0].agent.name || 'Unknown'}
                  email={agentAssignments[0].agent.email || ''}
                  clerkId={agentAssignments[0].agent.clerk_id || ''}
                  className='h-10 w-10'
                  fallbackClassName='bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300 text-xs font-medium'
                />
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <div className='text-center'>
                <div className='font-medium'>
                  {agentAssignments[0].agent.name || 'Unknown'}
                </div>
                <div className='text-xs opacity-80'>
                  {agentAssignments.length === 1
                    ? `Assigned to: ${agentAssignments[0].departments.map((dept) => DEPARTMENT_CONFIG[dept].label).join(', ')}`
                    : `${agentAssignments.length} agents assigned to departments`}
                </div>
              </div>
            </TooltipContent>
          </Tooltip>
        ) : (
          <Users className='w-10 h-10 text-muted-foreground p-2 bg-muted rounded-full' />
        )}

        <div className='flex-1'>
          <h4 className='text-sm font-medium'>Department Assignment Rules</h4>
          <p className='text-xs text-muted-foreground mt-1'>
            {activeRulesCount > 0
              ? `${activeRulesCount} department${activeRulesCount === 1 ? '' : 's'} configured with auto-assignment`
              : 'No department assignment rules are currently active'}
          </p>
        </div>

        <Badge variant={activeRulesCount > 0 ? 'default' : 'secondary'}>
          {activeRulesCount} Active
        </Badge>
      </div>

      {/* Department Rules */}
      <div className='space-y-4'>
        <Label className='text-sm font-medium'>
          Configure Department Rules
        </Label>

        <div className='grid gap-4'>
          {rules.map((rule) => {
            const config = DEPARTMENT_CONFIG[rule.department];

            return (
              <div
                key={rule.department}
                className={cn(
                  'p-4 transition-all duration-200 bg-white dark:bg-gray-800',
                  'rounded-lg border shadow-sm',
                  rule.is_active
                    ? 'border-primary/20'
                    : 'border-gray-200 dark:border-gray-700'
                )}
              >
                <div className='space-y-4'>
                  {/* Department Header */}
                  <div className='flex items-center justify-between'>
                    <div className='flex items-center gap-3'>
                      <Badge className={cn('text-xs', config.color)}>
                        <div
                          className={cn(
                            'w-1.5 h-1.5 rounded-full mr-1.5',
                            config.dotColor
                          )}
                        />
                        {config.label}
                      </Badge>
                      <ArrowRight className='w-4 h-4 text-muted-foreground' />
                      <span className='text-sm text-muted-foreground'>
                        {rule.assigned_agent_id
                          ? (() => {
                              const agent = agentDetails.find(
                                (a) => a.id === rule.assigned_agent_id
                              );
                              return agent?.name || 'Assigned Agent';
                            })()
                          : 'No Agent Assigned'}
                      </span>
                    </div>

                    {/* Switch Component with Status */}
                    {rule.assigned_agent_id ? (
                      <div className='flex items-center gap-2'>
                        <Switch
                          checked={rule.is_active}
                          onCheckedChange={() =>
                            handleToggleActive(rule.department)
                          }
                          disabled={isLoading}
                        />
                        <span
                          className={cn(
                            'text-xs font-medium',
                            rule.is_active
                              ? 'text-green-600 dark:text-green-400'
                              : 'text-gray-500 dark:text-gray-400'
                          )}
                        >
                          {rule.is_active ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                    ) : (
                      <div className='flex items-center gap-2'>
                        <div className='w-2 h-2 rounded-full bg-yellow-500' />
                        <span className='text-xs text-yellow-600 dark:text-yellow-400 font-medium'>
                          Not Set
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Agent Selection */}
                  <div className='space-y-2'>
                    <Label className='text-xs text-muted-foreground'>
                      Select agent for {config.label} tickets
                    </Label>
                    <UserAutocomplete
                      value={rule.assigned_agent_id || ''}
                      onChange={(value) =>
                        handleAgentChange(rule.department, value)
                      }
                      placeholder={`Select agent for ${config.label} department...`}
                      roleFilter={['admin', 'agent']}
                      multiple={false}
                      dropdownOnly={true}
                      returnUserIds={true}
                      disabled={isLoading}
                      className='h-9'
                    />
                  </div>

                  {/* Rule Description */}
                  <p className='text-xs text-muted-foreground'>
                    {rule.is_active && rule.assigned_agent_id
                      ? `All new ${config.label.toLowerCase()} tickets will be automatically assigned to ${(() => {
                          const agent = agentDetails.find(
                            (a) => a.id === rule.assigned_agent_id
                          );
                          return agent?.name || 'the selected agent';
                        })()}.`
                      : !rule.assigned_agent_id
                        ? `Select an agent to enable auto-assignment for ${config.label.toLowerCase()} tickets. Without an agent, tickets will use the default agent from Administration.`
                        : `Auto-assignment is temporarily disabled for ${config.label.toLowerCase()} tickets. Tickets will use the default agent from Administration.`}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Action Buttons */}
      <div className='flex items-center gap-3'>
        <Button
          onClick={handleSave}
          disabled={!hasChanges || isLoading}
          className='min-w-[100px]'
        >
          {isLoading ? (
            <div className='flex items-center gap-2'>
              <div className='w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin' />
              Saving...
            </div>
          ) : (
            <>
              <Save className='w-4 h-4 mr-2' />
              Save Rules
            </>
          )}
        </Button>

        <Button
          variant='outline'
          onClick={handleReset}
          disabled={!hasChanges || isLoading}
        >
          <RotateCcw className='w-4 h-4 mr-2' />
          Reset Changes
        </Button>
      </div>

      {/* Information Box */}
      <div className='bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4'>
        <div className='flex items-start gap-3'>
          <AlertCircle className='w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0' />
          <div className='space-y-2'>
            <h4 className='text-sm font-medium text-blue-900 dark:text-blue-100'>
              Assignment Priority Rules
            </h4>
            <ul className='text-xs text-blue-800 dark:text-blue-200 space-y-1'>
              <li>
                • Department-specific rules take priority over default agent
                assignment
              </li>
              <li>• Only active rules with assigned agents will be applied</li>
              <li>
                • If a department rule is inactive, the default agent will be
                used
              </li>
              <li>• Rules are processed in the order shown above</li>
              <li>• Changes take effect immediately for new tickets</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Change Indicator */}
      {hasChanges && (
        <div className='flex items-center gap-2 text-sm text-amber-600 dark:text-amber-400'>
          <AlertCircle className='w-4 h-4' />
          You have unsaved changes. Click &ldquo;Save Rules&rdquo; to apply
          them.
        </div>
      )}
    </div>
  );
};

export const DepartmentAssignmentRules = memo(
  DepartmentAssignmentRulesComponent
);
